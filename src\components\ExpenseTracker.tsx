import React, { useState } from 'react';
import { AppState, Expense } from '../types';
import { useAppState } from '../hooks/useAppState';
import { validateExpense } from '../utils/validation';
import { formatCurrency, isWithinBudget } from '../utils/calculations';
import { SUBCATEGORIES } from '../types';
import { Plus, Calendar, DollarSign, Tag, Heart, HeartOff, Trash2, Edit3, Receipt } from 'lucide-react';

interface ExpenseTrackerProps {
  state: AppState;
  actions: ReturnType<typeof useAppState>['actions'];
}

const ExpenseTracker: React.FC<ExpenseTrackerProps> = ({ state, actions }) => {
  const [showForm, setShowForm] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);
  const [formData, setFormData] = useState({
    amount: '',
    description: '',
    category: 'needs' as 'needs' | 'financialGoals' | 'fun',
    subcategory: '',
    isConscious: undefined as boolean | undefined,
    notes: '',
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const validation = validateExpense({
      amount: formData.amount,
      description: formData.description,
      category: formData.category,
    });

    if (!validation.isValid) {
      setErrors(validation.errors);
      setIsSubmitting(false);
      return;
    }

    const amount = parseFloat(formData.amount);
    
    // Check if expense is within budget
    if (state.budgetAllocation && !isWithinBudget(amount, formData.category, state.budgetAllocation, state.expenses)) {
      const confirmed = window.confirm(
        `This expense will put you over budget for ${formData.category}. Do you want to continue?`
      );
      if (!confirmed) {
        setIsSubmitting(false);
        return;
      }
    }

    const expense: Expense = {
      id: editingExpense?.id || crypto.randomUUID(),
      amount,
      description: formData.description.trim(),
      category: formData.category,
      subcategory: formData.subcategory || undefined,
      date: new Date(),
      isConscious: formData.category === 'fun' ? formData.isConscious : undefined,
      notes: formData.notes.trim() || undefined,
    };

    if (editingExpense) {
      actions.updateExpense(editingExpense.id, expense);
    } else {
      actions.addExpense(expense);
    }

    // Reset form
    setFormData({
      amount: '',
      description: '',
      category: 'needs',
      subcategory: '',
      isConscious: undefined,
      notes: '',
    });
    setErrors({});
    setShowForm(false);
    setEditingExpense(null);
    setIsSubmitting(false);
  };

  const handleEdit = (expense: Expense) => {
    setEditingExpense(expense);
    setFormData({
      amount: expense.amount.toString(),
      description: expense.description,
      category: expense.category,
      subcategory: expense.subcategory || '',
      isConscious: expense.isConscious,
      notes: expense.notes || '',
    });
    setShowForm(true);
  };

  const handleDelete = (expenseId: string) => {
    if (window.confirm('Are you sure you want to delete this expense?')) {
      actions.deleteExpense(expenseId);
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingExpense(null);
    setFormData({
      amount: '',
      description: '',
      category: 'needs',
      subcategory: '',
      isConscious: undefined,
      notes: '',
    });
    setErrors({});
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'needs':
        return 'bg-blue-100 text-blue-800';
      case 'financialGoals':
        return 'bg-green-100 text-green-800';
      case 'fun':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'needs':
        return 'Needs';
      case 'financialGoals':
        return 'Goals';
      case 'fun':
        return 'Fun';
      default:
        return category;
    }
  };

  const sortedExpenses = [...state.expenses].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
            <Receipt className="w-7 h-7 text-white" />
          </div>
          <div>
            <h2 className="text-3xl font-bold gradient-text">Expense Tracker</h2>
            <p className="text-gray-600 font-medium">Track your spending across all categories</p>
          </div>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Expense</span>
        </button>
      </div>

      {/* Add/Edit Expense Form */}
      {showForm && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {editingExpense ? 'Edit Expense' : 'Add New Expense'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount *
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="number"
                    value={formData.amount}
                    onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                    className={`input-field pl-10 ${errors.amount ? 'border-red-300' : ''}`}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                  />
                </div>
                {errors.amount && <p className="text-sm text-red-600 mt-1">{errors.amount}</p>}
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    category: e.target.value as 'needs' | 'financialGoals' | 'fun',
                    subcategory: '' // Reset subcategory when category changes
                  })}
                  className={`input-field ${errors.category ? 'border-red-300' : ''}`}
                >
                  <option value="needs">Needs (50%)</option>
                  <option value="financialGoals">Financial Goals (20%)</option>
                  <option value="fun">Fun (30%)</option>
                </select>
                {errors.category && <p className="text-sm text-red-600 mt-1">{errors.category}</p>}
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className={`input-field ${errors.description ? 'border-red-300' : ''}`}
                placeholder="What did you spend on?"
                maxLength={100}
              />
              {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
            </div>

            {/* Subcategory */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Subcategory (Optional)
              </label>
              <select
                value={formData.subcategory}
                onChange={(e) => setFormData({ ...formData, subcategory: e.target.value })}
                className="input-field"
              >
                <option value="">Select a subcategory</option>
                {SUBCATEGORIES[formData.category].map((sub) => (
                  <option key={sub} value={sub}>{sub}</option>
                ))}
              </select>
            </div>

            {/* Conscious Spending (only for Fun category) */}
            {formData.category === 'fun' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  How do you feel about this spending?
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="isConscious"
                      checked={formData.isConscious === true}
                      onChange={() => setFormData({ ...formData, isConscious: true })}
                      className="mr-2"
                    />
                    <Heart className="w-4 h-4 text-red-500 mr-1" />
                    <span className="text-sm">I loved this!</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="isConscious"
                      checked={formData.isConscious === false}
                      onChange={() => setFormData({ ...formData, isConscious: false })}
                      className="mr-2"
                    />
                    <HeartOff className="w-4 h-4 text-gray-500 mr-1" />
                    <span className="text-sm">I didn't care about this</span>
                  </label>
                </div>
              </div>
            )}

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes (Optional)
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                className="input-field"
                rows={2}
                placeholder="Any additional notes..."
                maxLength={200}
              />
            </div>

            {/* Form Actions */}
            <div className="flex space-x-3">
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn-primary disabled:opacity-50"
              >
                {isSubmitting ? 'Saving...' : editingExpense ? 'Update Expense' : 'Add Expense'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Expenses List */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Recent Expenses ({state.expenses.length})
        </h3>
        
        {sortedExpenses.length === 0 ? (
          <div className="text-center py-8">
            <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No expenses recorded yet</p>
            <p className="text-sm text-gray-500">Add your first expense to get started</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedExpenses.map((expense) => (
              <div key={expense.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="text-lg font-semibold text-gray-900">
                        {formatCurrency(expense.amount)}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(expense.category)}`}>
                        {getCategoryLabel(expense.category)}
                      </span>
                      {expense.subcategory && (
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {expense.subcategory}
                        </span>
                      )}
                      {expense.category === 'fun' && expense.isConscious !== undefined && (
                        <span className="flex items-center">
                          {expense.isConscious ? (
                            <Heart className="w-4 h-4 text-red-500" />
                          ) : (
                            <HeartOff className="w-4 h-4 text-gray-400" />
                          )}
                        </span>
                      )}
                    </div>
                    <p className="text-gray-700 font-medium">{expense.description}</p>
                    {expense.notes && (
                      <p className="text-sm text-gray-500 mt-1">{expense.notes}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(expense.date).toLocaleDateString()} at {new Date(expense.date).toLocaleTimeString()}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(expense)}
                      className="p-2 text-gray-400 hover:text-gray-600"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(expense.id)}
                      className="p-2 text-gray-400 hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExpenseTracker;
