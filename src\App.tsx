import React from 'react';
import { useAppState } from './hooks/useAppState';
import Dashboard from './components/Dashboard';
import IncomeSetup from './components/IncomeSetup';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorMessage from './components/ErrorMessage';
import { DollarSign, User, Settings } from 'lucide-react';

function App() {
  const { state, actions } = useAppState();

  if (state.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage
          message={state.error}
          onRetry={() => actions.setError(null)}
        />
      </div>
    );
  }

  // Show income setup if no user or no income set
  if (!state.user || state.user.monthlyIncome <= 0) {
    return (
      <div className="min-h-screen">
        <IncomeSetup
          onIncomeSet={actions.setMonthlyIncome}
          currentIncome={state.user?.monthlyIncome || 0}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <DollarSign className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold gradient-text">
                  Fund Manager
                </h1>
                <p className="text-sm text-gray-500 font-medium">
                  Personal Finance Dashboard
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-right">
                <p className="text-sm text-gray-500 font-medium">Monthly Income</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  ${state.user.monthlyIncome.toLocaleString()}
                </p>
              </div>
              <button
                onClick={() => actions.setMonthlyIncome(0)}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg"
              >
                <Settings className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Edit Income</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Dashboard 
          state={state}
          actions={actions}
        />
      </main>

      <footer className="bg-white/60 backdrop-blur-md border-t border-white/30 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm font-medium text-gray-700">
                Built with the 50/20/30 Rule, The Boring Rule, and Conscious Spending principles
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Empowering smart financial decisions through proven methodologies
              </p>
            </div>
            <button
              onClick={actions.resetData}
              className="px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-sm font-medium rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
            >
              Reset All Data
            </button>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
