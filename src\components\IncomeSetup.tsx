import React, { useState } from 'react';
import { DollarSign, TrendingUp, Target, Heart } from 'lucide-react';
import { validateIncome } from '../utils/validation';
import { formatCurrency } from '../utils/calculations';

interface IncomeSetupProps {
  onIncomeSet: (income: number) => void;
  currentIncome: number;
}

const IncomeSetup: React.FC<IncomeSetupProps> = ({ onIncomeSet, currentIncome }) => {
  const [income, setIncome] = useState(currentIncome > 0 ? currentIncome.toString() : '');
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const validation = validateIncome(income);
    if (!validation.isValid) {
      setErrors(validation.errors);
      setIsSubmitting(false);
      return;
    }

    const numericIncome = parseFloat(income);
    onIncomeSet(numericIncome);
    setIsSubmitting(false);
  };

  const handleIncomeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIncome(value);
    
    // Clear errors when user starts typing
    if (errors.income) {
      setErrors({});
    }
  };

  const previewAllocation = income && !errors.income ? {
    needs: parseFloat(income) * 0.5,
    goals: parseFloat(income) * 0.2,
    fun: parseFloat(income) * 0.3,
  } : null;

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <div className="text-center mb-12">
          <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
            <DollarSign className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-5xl font-bold gradient-text mb-6">
            Welcome to Fund Manager
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-lg mx-auto">
            Let's start by setting up your monthly income to create your personalized budget using the proven 50/20/30 rule
          </p>
        </div>

        <div className="card mb-12">
          <form onSubmit={handleSubmit} className="space-y-8">
            <div>
              <label htmlFor="income" className="block text-lg font-semibold text-gray-800 mb-4">
                Monthly Net Income
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <DollarSign className="h-6 w-6 text-gray-400" />
                </div>
                <input
                  type="number"
                  id="income"
                  value={income}
                  onChange={handleIncomeChange}
                  placeholder="Enter your monthly income"
                  className={`input-field pl-12 text-xl ${errors.income ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}`}
                  min="0"
                  step="0.01"
                />
              </div>
              {errors.income && (
                <p className="mt-1 text-sm text-red-600">{errors.income}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                Enter your take-home pay after taxes and deductions
              </p>
            </div>

            <button
              type="submit"
              disabled={isSubmitting || !income}
              className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Setting up...' : 'Set Up My Budget'}
            </button>
          </form>
        </div>

        {previewAllocation && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Your Budget Preview (50/20/30 Rule)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-900">Needs (50%)</span>
                </div>
                <p className="text-2xl font-bold text-blue-900">
                  {formatCurrency(previewAllocation.needs)}
                </p>
                <p className="text-sm text-blue-700">
                  Rent, groceries, utilities, transportation
                </p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-900">Goals (20%)</span>
                </div>
                <p className="text-2xl font-bold text-green-900">
                  {formatCurrency(previewAllocation.goals)}
                </p>
                <p className="text-sm text-green-700">
                  Savings, investments, debt repayment
                </p>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Heart className="w-5 h-5 text-purple-600" />
                  <span className="font-medium text-purple-900">Fun (30%)</span>
                </div>
                <p className="text-2xl font-bold text-purple-900">
                  {formatCurrency(previewAllocation.fun)}
                </p>
                <p className="text-sm text-purple-700">
                  Entertainment, dining out, hobbies
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Don't worry, you can always adjust your income later
          </p>
        </div>
      </div>
    </div>
  );
};

export default IncomeSetup;
