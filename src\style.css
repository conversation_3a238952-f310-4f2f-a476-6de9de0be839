@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  body {
    font-family: 'Inter', sans-serif;
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-500/30 transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-semibold py-3 px-6 rounded-xl border border-gray-200 shadow-md hover:shadow-lg transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-gray-500/20 transform hover:-translate-y-0.5;
  }

  .btn-danger {
    @apply bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-red-500/30;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 placeholder-gray-400 shadow-sm;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 hover:shadow-2xl transition-all duration-300;
  }

  .card-compact {
    @apply bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-white/30 p-4 hover:shadow-xl transition-all duration-300;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-bold;
  }

  .stat-card {
    @apply bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-6 shadow-lg border border-white/50 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }

  .progress-bar {
    @apply h-3 bg-gray-200 rounded-full overflow-hidden shadow-inner;
  }

  .progress-fill {
    @apply h-full rounded-full transition-all duration-700 ease-out;
  }

  .tab-button {
    @apply flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-500/20;
  }

  .tab-active {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg;
  }

  .tab-inactive {
    @apply text-gray-600 hover:text-gray-800 hover:bg-white/60;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .floating-card {
    @apply transform hover:scale-105 transition-transform duration-300;
  }
}
