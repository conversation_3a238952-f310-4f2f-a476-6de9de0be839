import React, { useState } from 'react';
import type { AppState } from '../types';
import { useAppState } from '../hooks/useAppState';
import BudgetOverview from './BudgetOverview';
import ExpenseTracker from './ExpenseTracker';
import InvestmentTracker from './InvestmentTracker';
import ConsciousSpendingAnalysis from './ConsciousSpendingAnalysis';
import { PlusCircle, TrendingUp, Target, Heart, BarChart3 } from 'lucide-react';

interface DashboardProps {
  state: AppState;
  actions: ReturnType<typeof useAppState>['actions'];
}

const Dashboard: React.FC<DashboardProps> = ({ state, actions }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'expenses' | 'investment' | 'analysis'>('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'expenses', label: 'Expenses', icon: PlusCircle },
    { id: 'investment', label: 'Investment', icon: TrendingUp },
    { id: 'analysis', label: 'Spending Analysis', icon: Heart },
  ] as const;

  return (
    <div className="space-y-8">
      {/* Tab Navigation */}
      <div className="bg-white/60 backdrop-blur-md rounded-2xl p-2 shadow-lg border border-white/30">
        <nav className="flex space-x-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  tab-button
                  ${isActive ? 'tab-active' : 'tab-inactive'}
                `}
              >
                <Icon className="w-5 h-5" />
                <span className="font-semibold">{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {activeTab === 'overview' && (
          <BudgetOverview 
            state={state}
            onSwitchToExpenses={() => setActiveTab('expenses')}
            onSwitchToInvestment={() => setActiveTab('investment')}
          />
        )}
        
        {activeTab === 'expenses' && (
          <ExpenseTracker 
            state={state}
            actions={actions}
          />
        )}
        
        {activeTab === 'investment' && (
          <InvestmentTracker 
            state={state}
            actions={actions}
          />
        )}
        
        {activeTab === 'analysis' && (
          <ConsciousSpendingAnalysis 
            expenses={state.expenses}
          />
        )}
      </div>
    </div>
  );
};

export default Dashboard;
