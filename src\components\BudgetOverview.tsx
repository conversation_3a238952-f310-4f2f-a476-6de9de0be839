import React from 'react';
import { AppState } from '../types';
import { 
  calculateSpendingByCategory, 
  calculateRemainingBudget, 
  calculateBudgetUtilization,
  formatCurrency,
  formatPercentage,
  getBudgetStatus
} from '../utils/calculations';
import { Target, TrendingUp, Heart, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

interface BudgetOverviewProps {
  state: AppState;
  onSwitchToExpenses: () => void;
  onSwitchToInvestment: () => void;
}

const BudgetOverview: React.FC<BudgetOverviewProps> = ({ 
  state, 
  onSwitchToExpenses, 
  onSwitchToInvestment 
}) => {
  if (!state.budgetAllocation || !state.user) {
    return (
      <div className="card">
        <p className="text-gray-600">No budget data available</p>
      </div>
    );
  }

  const spending = calculateSpendingByCategory(state.expenses);
  const remaining = calculateRemainingBudget(state.budgetAllocation, state.expenses);
  const utilization = calculateBudgetUtilization(state.budgetAllocation, state.expenses);

  // Data for pie chart
  const pieData = [
    { name: 'Needs', value: spending.needs, color: '#3B82F6', allocated: state.budgetAllocation.needs },
    { name: 'Goals', value: spending.financialGoals, color: '#10B981', allocated: state.budgetAllocation.financialGoals },
    { name: 'Fun', value: spending.fun, color: '#8B5CF6', allocated: state.budgetAllocation.fun },
  ];

  // Data for bar chart
  const barData = [
    {
      category: 'Needs',
      allocated: state.budgetAllocation.needs,
      spent: spending.needs,
      remaining: remaining.needs,
    },
    {
      category: 'Goals',
      allocated: state.budgetAllocation.financialGoals,
      spent: spending.financialGoals,
      remaining: remaining.financialGoals,
    },
    {
      category: 'Fun',
      allocated: state.budgetAllocation.fun,
      spent: spending.fun,
      remaining: remaining.fun,
    },
  ];

  const getStatusIcon = (category: 'needs' | 'financialGoals' | 'fun') => {
    const status = getBudgetStatus(category, state.budgetAllocation!, state.expenses);
    switch (status) {
      case 'over':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'near':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
  };

  const getStatusColor = (category: 'needs' | 'financialGoals' | 'fun') => {
    const status = getBudgetStatus(category, state.budgetAllocation!, state.expenses);
    switch (status) {
      case 'over':
        return 'bg-red-50 border-red-200';
      case 'near':
        return 'bg-yellow-50 border-yellow-200';
      default:
        return 'bg-green-50 border-green-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Needs Card */}
        <div className="stat-card floating-card">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Target className="w-7 h-7 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Needs</h3>
                <p className="text-sm text-gray-500 font-medium">50% of income</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusIcon('needs')}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Allocated</span>
              <span className="text-lg font-semibold text-gray-700">{formatCurrency(state.budgetAllocation.needs)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Spent</span>
              <span className="text-2xl font-bold text-gray-900">{formatCurrency(spending.needs)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Remaining</span>
              <span className={`text-lg font-bold ${remaining.needs >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(remaining.needs)}
              </span>
            </div>
          </div>

          <div className="mt-6">
            <div className="progress-bar">
              <div
                className={`progress-fill ${utilization.needs > 100 ? 'bg-gradient-to-r from-red-500 to-red-600' : utilization.needs > 80 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-gradient-to-r from-blue-500 to-blue-600'}`}
                style={{ width: `${Math.min(utilization.needs, 100)}%` }}
              />
            </div>
            <p className="text-sm font-medium text-gray-600 mt-2">
              {formatPercentage(utilization.needs)} used
            </p>
          </div>
        </div>

        {/* Financial Goals Card */}
        <div className="stat-card floating-card">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                <TrendingUp className="w-7 h-7 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Goals</h3>
                <p className="text-sm text-gray-500 font-medium">20% of income</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusIcon('financialGoals')}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Allocated</span>
              <span className="text-lg font-semibold text-gray-700">{formatCurrency(state.budgetAllocation.financialGoals)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Spent</span>
              <span className="text-2xl font-bold text-gray-900">{formatCurrency(spending.financialGoals)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Remaining</span>
              <span className={`text-lg font-bold ${remaining.financialGoals >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(remaining.financialGoals)}
              </span>
            </div>
          </div>

          <div className="mt-6">
            <div className="progress-bar">
              <div
                className={`progress-fill ${utilization.financialGoals > 100 ? 'bg-gradient-to-r from-red-500 to-red-600' : utilization.financialGoals > 80 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-gradient-to-r from-green-500 to-emerald-600'}`}
                style={{ width: `${Math.min(utilization.financialGoals, 100)}%` }}
              />
            </div>
            <p className="text-sm font-medium text-gray-600 mt-2">
              {formatPercentage(utilization.financialGoals)} used
            </p>
          </div>
          
          <button
            onClick={onSwitchToInvestment}
            className="mt-3 text-sm text-green-600 hover:text-green-700 font-medium"
          >
            Manage Investments →
          </button>
        </div>

        {/* Fun Card */}
        <div className={`card border-2 ${getStatusColor('fun')}`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Heart className="w-6 h-6 text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-900">Fun (30%)</h3>
            </div>
            {getStatusIcon('fun')}
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Allocated:</span>
              <span className="font-medium">{formatCurrency(state.budgetAllocation.fun)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Spent:</span>
              <span className="font-medium">{formatCurrency(spending.fun)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Remaining:</span>
              <span className={`font-medium ${remaining.fun >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(remaining.fun)}
              </span>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="progress-bar">
              <div 
                className={`progress-fill ${utilization.fun > 100 ? 'bg-red-500' : utilization.fun > 80 ? 'bg-yellow-500' : 'bg-purple-500'}`}
                style={{ width: `${Math.min(utilization.fun, 100)}%` }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {formatPercentage(utilization.fun)} used
            </p>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pie Chart */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Spending Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center space-x-4 mt-4">
            {pieData.map((entry) => (
              <div key={entry.name} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-sm text-gray-600">{entry.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Bar Chart */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget vs Spending</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Bar dataKey="allocated" fill="#E5E7EB" name="Allocated" />
                <Bar dataKey="spent" fill="#3B82F6" name="Spent" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={onSwitchToExpenses}
            className="btn-primary"
          >
            Add Expense
          </button>
          <button
            onClick={onSwitchToInvestment}
            className="btn-secondary"
          >
            Update Investment
          </button>
        </div>
      </div>
    </div>
  );
};

export default BudgetOverview;
